<?php

namespace Modules\Core\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use LucasDotVin\Soulbscription\Enums\PeriodicityType;

class CreateOrganizationPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware/policies
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'grace_days' => ['nullable', 'integer', 'min:0', 'max:365'],
            'periodicity' => ['nullable', 'integer', 'min:1'],
            'periodicity_type' => ['nullable', 'string', 'in:day,week,month,year'],
            'quota' => ['nullable', 'integer', 'min:0'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Tên plan là bắt buộc',
            'name.max' => 'Tên plan không được vượt quá 255 ký tự',
            'description.max' => 'Mô tả không được vượt quá 1000 ký tự',
            'grace_days.integer' => 'Số ngày gia hạn phải là số nguyên',
            'grace_days.min' => 'Số ngày gia hạn không được âm',
            'grace_days.max' => 'Số ngày gia hạn không được vượt quá 365 ngày',
            'periodicity.integer' => 'Chu kỳ phải là số nguyên',
            'periodicity.min' => 'Chu kỳ phải lớn hơn 0',
            'periodicity_type.in' => 'Loại chu kỳ phải là: day, week, month, year',
            'quota.integer' => 'Quota phải là số nguyên',
            'quota.min' => 'Quota không được âm',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $this->merge([
            'grace_days' => $this->grace_days ?? 0,
            'periodicity' => $this->periodicity ?? 1,
            'periodicity_type' => $this->periodicity_type ?? PeriodicityType::Month->value,
        ]);
    }
}
