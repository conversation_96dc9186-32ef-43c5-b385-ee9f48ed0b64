<?php

namespace Modules\Core\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use LucasDotVin\Soulbscription\Enums\PeriodicityType;
use LucasDotVin\Soulbscription\Models\Feature;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Core\Http\Requests\CreateOrganizationPlanRequest;
use Modules\Core\Http\Requests\UpdateOrganizationPlanRequest;
use Modules\Core\Models\Plan;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Symfony\Component\HttpFoundation\Response;

/**
 * Controller for managing Organization Plans
 */
class OrganizationPlanController extends Controller
{

    /**
     * Get all plans available to an organization
     */
    public function index(Organization $organization): Response
    {
        $plans = Plan::availableToOrganization($organization->id)
            ->with(['features', 'organization'])
            ->orderBy('created_at', 'desc')
            ->get();

        return ResponseHelper::success(data: $plans);
    }

    /**
     * Get organization-specific plans with pagination
     */
    public function organizationPlans(Organization $organization, Request $request): Response
    {
        $perPage = $request->get('per_page', 15);
        $plans = Plan::forOrganization($organization->id)
            ->with(['features', 'subscriptions'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return ResponseHelper::success(data: $plans);
    }

    /**
     * Get system plans (templates)
     */
    public function systemPlans(): Response
    {
        $plans = Plan::systemPlans()
            ->with('features')
            ->orderBy('created_at', 'desc')
            ->get();

        return ResponseHelper::success(data: $plans);
    }

    /**
     * Create a new organization plan
     */
    public function store(Organization $organization, CreateOrganizationPlanRequest $request): Response
    {
        try {
            $data = $request->validated();

            $planData = [
                'organization_id' => $organization->id,
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'grace_days' => $data['grace_days'] ?? 0,
                'periodicity' => $data['periodicity'] ?? 1,
                'periodicity_type' => $data['periodicity_type'] ?? PeriodicityType::Month,
            ];

            $plan = Plan::create($planData);

            // Auto-attach transactions feature with quota as charges
            $transactionsFeature = Feature::where('name', 'transactions')->first();
            if ($transactionsFeature) {
                $plan->features()->attach($transactionsFeature->id, [
                    'charges' => $data['quota'] ?? null,
                ]);
            }



            return ResponseHelper::success(
                message: 'Plan created successfully',
                data: $plan->load('features'),
                httpCode: 201
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to create plan: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Show a specific plan
     */
    public function show(Organization $organization, Plan $plan): Response
    {
        // Ensure plan belongs to organization or is a system plan
        if ($plan->isOrganizationPlan() && $plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not found',
                httpCode: 404
            );
        }

        $plan->load(['features', 'organization', 'subscriptions']);

        return ResponseHelper::success(data: $plan);
    }

    /**
     * Update an organization plan
     */
    public function update(Organization $organization, Plan $plan, UpdateOrganizationPlanRequest $request): Response
    {
        // Ensure plan belongs to organization
        if ($plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not found',
                httpCode: 404
            );
        }

        // Ensure this is an organization plan
        if ($plan->isSystemPlan()) {
            return ResponseHelper::error(
                message: 'Cannot update system plans',
                httpCode: 422
            );
        }

        try {
            $data = $request->validated();

            $updateData = array_filter([
                'name' => $data['name'] ?? null,
                'description' => $data['description'] ?? null,
                'grace_days' => $data['grace_days'] ?? null,
                'periodicity' => $data['periodicity'] ?? null,
                'periodicity_type' => $data['periodicity_type'] ?? null,
                'is_active' => $data['is_active'] ?? null,
            ], fn($value) => $value !== null);

            $plan->update($updateData);

            // Update transactions feature quota if provided
            if (isset($data['quota'])) {
                $transactionsFeature = Feature::where('name', 'transactions')->first();
                if ($transactionsFeature) {
                    $plan->features()->detach($transactionsFeature->id);
                    $plan->features()->attach($transactionsFeature->id, [
                        'charges' => $data['quota'],
                    ]);
                }
            }

            return ResponseHelper::success(
                message: 'Plan updated successfully',
                data: $plan->load('features')
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to update plan: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Toggle plan status (enable/disable)
     */
    public function toggleStatus(Organization $organization, Plan $plan): Response
    {
        // Ensure plan belongs to organization
        if ($plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not found',
                httpCode: 404
            );
        }

        // Ensure this is an organization plan
        if ($plan->isSystemPlan()) {
            return ResponseHelper::error(
                message: 'Cannot modify system plans',
                httpCode: 422
            );
        }

        try {
            $plan->update(['is_active' => !$plan->is_active]);

            $status = $plan->is_active ? 'kích hoạt' : 'vô hiệu hóa';

            return ResponseHelper::success(
                message: "Đã {$status} gói dịch vụ thành công",
                data: $plan->load('features')
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to toggle plan status: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Delete an organization plan
     */
    public function destroy(Organization $organization, Plan $plan): Response
    {
        // Ensure plan belongs to organization
        if ($plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not found',
                httpCode: 404
            );
        }

        // Ensure this is an organization plan
        if ($plan->isSystemPlan()) {
            return ResponseHelper::error(
                message: 'Cannot delete system plans',
                httpCode: 422
            );
        }

        // Check if plan has active subscriptions
        if ($plan->subscriptions()->notCanceled()->exists()) {
            return ResponseHelper::error(
                message: 'Cannot delete plan with active subscriptions',
                httpCode: 422
            );
        }

        try {
            $plan->delete();

            return ResponseHelper::success(
                message: 'Plan deleted successfully'
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to delete plan: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Get plan usage statistics
     */
    public function usageStats(Organization $organization): Response
    {
        $plans = Plan::forOrganization($organization->id)
            ->with('subscriptions')
            ->get();

        $stats = [];
        foreach ($plans as $plan) {
            $activeSubscriptions = $plan->subscriptions()->notCanceled()->count();
            $totalSubscriptions = $plan->subscriptions()->count();

            $stats[] = [
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
                'active_subscriptions' => $activeSubscriptions,
                'total_subscriptions' => $totalSubscriptions,
                'features_count' => $plan->features()->count(),
            ];
        }

        return ResponseHelper::success(data: $stats);
    }

    /**
     * Get organization subscribers
     */
    public function subscribers(Organization $organization): Response
    {
        $planIds = Plan::forOrganization($organization->id)->pluck('id');

        $subscribers = User::whereHas('subscriptions', function ($query) use ($planIds) {
            $query->whereIn('plan_id', $planIds)->notCanceled();
        })->with(['subscriptions' => function ($query) use ($planIds) {
            $query->whereIn('plan_id', $planIds)->notCanceled()->with('plan');
        }])->get();

        return ResponseHelper::success(data: $subscribers);
    }
}
